import { useState, useEffect } from "react";
import { useParams, Link, useSearchParams } from "react-router-dom";
import { stateService } from "../../services/api/state.service";
import { warService } from "../../services/api/war.service";
import { stateElectionService } from "../../services/api/stateElection.service";
import Navbar from "../Navbar";
import useUserDataStore from "../../store/useUserDataStore";
import {
  FaCheck,
  FaTimes,
  FaEdit,
  FaVoteYea,
  FaFistRaised,
  FaCog,
  FaGlobe,
  FaLock,
  FaUnlock,
} from "react-icons/fa";
import { showErrorToast } from "../../utils/showErrorToast";
import { showSuccessToast } from "../../utils/showSuccessToast";
import { GovernmentType } from "../../types/state";
import ActiveElection from "../elections/ActiveElection";
import ElectionHistory from "../elections/ElectionHistory";
import Footer from "../common/Footer";

const StateDetail = () => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const [state, setState] = useState(null);
  const [resources, setResources] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { userData: user } = useUserDataStore();
  const [isEditingName, setIsEditingName] = useState(false);
  const [newName, setNewName] = useState("");
  const [isUpdatingName, setIsUpdatingName] = useState(false);

  // Tab management - check for tab query parameter
  const tabFromUrl = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabFromUrl || 'overview');

  // Elections data
  const [activeElection, setActiveElection] = useState(null);
  const [electionsLoading, setElectionsLoading] = useState(false);

  // Wars data
  const [stateWars, setStateWars] = useState([]);
  const [warsLoading, setWarsLoading] = useState(false);

  // Government management
  const [isUpdatingGovernment, setIsUpdatingGovernment] = useState(false);
  const [isUpdatingBorders, setIsUpdatingBorders] = useState(false);
  const [showGovernmentModal, setShowGovernmentModal] = useState(false);
  const [pendingGovernmentType, setPendingGovernmentType] = useState(null);

  // Check if user is the state leader
  const isStateLeader = state?.leader?.id === user?.id;

  useEffect(() => {
    const fetchStateData = async () => {
      try {
        setLoading(true);
        const [stateData, resourcesData] = await Promise.all([
          stateService.getState(id),
          stateService.getStateResources(id),
        ]);
        setState(stateData);
        setResources(resourcesData);
        setError(null);
      } catch (err) {
        console.error("Error fetching state details:", err);
        setError("Failed to load state details. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchStateData();
    }
  }, [id]);

  // Handle tab from URL parameter
  useEffect(() => {
    if (tabFromUrl && state?.id) {
      if (tabFromUrl === 'elections' && !activeElection) {
        fetchElectionsData();
      }
      if (tabFromUrl === 'wars' && stateWars.length === 0) {
        fetchWarsData();
      }
    }
  }, [tabFromUrl, state?.id]);

  // State editing functions
  const handleEditName = () => {
    setNewName(state?.name || "");
    setIsEditingName(true);
  };

  const handleCancelEdit = () => {
    setIsEditingName(false);
    setNewName("");
  };

  const handleSaveName = async () => {
    if (!newName.trim()) {
      showErrorToast("Name cannot be empty");
      return;
    }

    if (newName === state?.name) {
      setIsEditingName(false);
      return;
    }

    setIsUpdatingName(true);
    try {
      await stateService.updateState(state.id,{
        name: newName.trim(),
      });
      showSuccessToast("Name updated successfully!");
      state.name = newName.trim();
      setIsEditingName(false);
      setNewName("");
    } catch (error) {
      showErrorToast(error || "Failed to update Name");
    } finally {
      setIsUpdatingName(false);
    }
  };

  const handleNameKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSaveName();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  // Fetch elections data
  const fetchElectionsData = async () => {
    if (!state?.id) return;

    try {
      setElectionsLoading(true);
      const election = await stateElectionService.getActiveElection(state.id);
      setActiveElection(election);
    } catch (err) {
      console.error("Error fetching elections:", err);
    } finally {
      setElectionsLoading(false);
    }
  };

  // Fetch wars data
  const fetchWarsData = async () => {
    if (!state?.id) return;

    try {
      setWarsLoading(true);
      const wars = await warService.getCurrentStateWars(state.id);
      setStateWars(wars);
    } catch (err) {
      console.error("Error fetching wars:", err);
    } finally {
      setWarsLoading(false);
    }
  };

  // Handle government type change
  const handleGovernmentTypeChange = (newType) => {
    if (!state?.id || !isStateLeader) return;

    // If changing to republic, show confirmation modal
    if (newType === GovernmentType.REPUBLIC) {
      setPendingGovernmentType(newType);
      setShowGovernmentModal(true);
    } else {
      // For dictatorship, change directly
      confirmGovernmentTypeChange(newType);
    }
  };

  // Confirm and execute government type change
  const confirmGovernmentTypeChange = async (newType) => {
    try {
      setIsUpdatingGovernment(true);
      const updatedState = await stateService.changeGovernmentType(state.id, newType);
      setState(updatedState);

      if (newType === GovernmentType.REPUBLIC) {
        showSuccessToast(`Government type changed to ${newType}. Elections have started automatically!`);
      } else {
        showSuccessToast(`Government type changed to ${newType}`);
      }
    } catch (err) {
      console.error("Error changing government type:", err);
      showErrorToast("Failed to change government type");
    } finally {
      setIsUpdatingGovernment(false);
      setShowGovernmentModal(false);
      setPendingGovernmentType(null);
    }
  };

  // Handle modal confirmation
  const handleModalConfirm = () => {
    if (pendingGovernmentType) {
      confirmGovernmentTypeChange(pendingGovernmentType);
    }
  };

  // Handle modal cancel
  const handleModalCancel = () => {
    setShowGovernmentModal(false);
    setPendingGovernmentType(null);
  };

  // Handle border settings change
  const handleBorderToggle = async () => {
    if (!state?.id || !isStateLeader) return;

    try {
      setIsUpdatingBorders(true);
      const newBorderStatus = !state.hasOpenBorders;
      const updatedState = await stateService.updateBorderSettings(state.id, newBorderStatus);
      setState(updatedState);
      showSuccessToast(`Borders are now ${newBorderStatus ? 'open' : 'closed'}`);
    } catch (err) {
      console.error("Error updating border settings:", err);
      showErrorToast("Failed to update border settings");
    } finally {
      setIsUpdatingBorders(false);
    }
  };

  // Define tabs
  const tabs = [
    { id: 'overview', label: 'Overview', icon: FaGlobe },
    { id: 'elections', label: 'Elections', icon: FaVoteYea },
    { id: 'wars', label: 'Wars', icon: FaFistRaised },
    ...(isStateLeader ? [{ id: 'government', label: 'Government', icon: FaCog }] : [])
  ];

  if (loading)
    return (
      <>
        <Navbar />
        <div className="flex justify-center items-center min-h-screen bg-gray-900">
          <div className="loader text-blue-500">Loading...</div>
        </div>
      </>
    );

  if (error)
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gray-900 p-4">
          <div className="text-red-500 bg-red-900/20 p-4 rounded-lg">
            {error}
          </div>
        </div>
      </>
    );

  if (!state)
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gray-900 p-4">
          <div className="text-gray-300 bg-gray-800/50 p-4 rounded-lg">
            State not found
          </div>
        </div>
      </>
    );

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gray-900 p-4">
        <div className="max-w-6xl mx-auto">
          <div className="mb-6">
            <Link
              to="/states"
              className="text-blue-500 hover:text-blue-400 flex items-center"
            >
              <span className="mr-2">←</span> Back to States
            </Link>
          </div>

          <div className="bg-gray-800 rounded-lg shadow-xl p-6">
            {/* Header Section */}
            <div className="flex items-center mb-8">
              {state.flagUrl && (
                <img
                  src={state.flagUrl}
                  alt={`${state.name} flag`}
                  className="w-20 h-20 object-cover rounded-lg mr-6 border-2 border-gray-700"
                />
              )}
              <div>
                {isEditingName ? (
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={newName}
                      onChange={(e) => setNewName(e.target.value)}
                      onKeyDown={handleNameKeyPress}
                      className="w-30 font-bold bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 focus:border-neonBlue focus:outline-none"
                      placeholder="Enter Name"
                      maxLength={20}
                      disabled={isUpdatingName}
                    />
                    <button
                      onClick={handleSaveName}
                      disabled={isUpdatingName}
                      className="text-green-400 hover:text-green-300 p-2 disabled:opacity-50"
                      title="Save Name"
                    >
                      <FaCheck />
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      disabled={isUpdatingName}
                      className="text-red-400 hover:text-red-300 p-2 disabled:opacity-50"
                      title="Cancel edit"
                    >
                      <FaTimes />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <h1 className="text-3xl font-bold text-neonBlue">
                      {state?.name}
                    </h1>
                    {isStateLeader && (
                      <button
                        onClick={handleEditName}
                        className="text-gray-400 hover:text-neonBlue p-1 ml-2"
                        title="Edit Name"
                      >
                        <FaEdit />
                      </button>
                    )}
                  </div>
                )}
                <p className="text-gray-400">
                  Led by
                  {state?.leader?.username ? (
                  <Link className="p-2" to={`/users/${state?.leader?.id}`}>
                    <span className="text-blue-400">
                      {state.leader.username}
                    </span>
                  </Link>
                  ):(
                    <span className="text-gray-400"> Unknown</span>
                  )}
                </p>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="mb-6">
              <div className="flex border-b border-gray-700">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => {
                        setActiveTab(tab.id);
                        if (tab.id === 'elections' && !activeElection) {
                          fetchElectionsData();
                        }
                        if (tab.id === 'wars' && stateWars.length === 0) {
                          fetchWarsData();
                        }
                      }}
                      className={`
                        flex items-center space-x-2 px-6 py-4 font-medium transition-colors
                        ${activeTab === tab.id
                          ? 'text-blue-400 border-b-2 border-blue-400 bg-blue-900/20'
                          : 'text-gray-400 hover:text-gray-300'
                        }
                      `}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{tab.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Column */}
                <div>
                  <div className="bg-gray-700/30 rounded-lg p-6 mb-6">
                    <h2 className="text-xl font-semibold text-white mb-4">
                      Regions ({state.regions.length})
                    </h2>
                    {state.regions.length === 0 ? (
                      <p className="text-gray-400">No regions in this state.</p>
                    ) : (
                      <div className="space-y-3">
                        {state.regions.map((region) => (
                          <div
                            key={region.id}
                            className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors"
                          >
                            <Link
                              to={`/regions/${region.id}`}
                              className="flex items-center justify-between"
                            >
                              <div>
                                <h3 className="text-white font-medium">
                                  {region.name}
                                </h3>
                                <p className="text-gray-400 text-sm">
                                  Population:{" "}
                                  {region.population?.toLocaleString() ||
                                    "Unknown"}
                                </p>
                              </div>
                              <div className="text-blue-400">→</div>
                            </Link>

                            {/* Parties Section */}
                            <div className="mt-3 border-t border-gray-700 pt-3">
                              <h4 className="text-gray-300 text-sm font-medium mb-2">
                                Parties:
                              </h4>
                              {region.parties && region.parties.length > 0 ? (
                                <div className="space-y-2">
                                  {region.parties.map((party) => (
                                    <div
                                      key={party.id}
                                      className="flex items-center"
                                    >
                                      <Link
                                        to={`/party/${party.id}`}
                                        className="text-blue-400 hover:text-blue-300 text-sm"
                                      >
                                        {party.name}
                                      </Link>
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <p className="text-gray-500 text-sm italic">
                                  No parties in this region
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Right Column */}
                <div>
                  {resources && (
                    <div className="bg-gray-700/30 rounded-lg p-6 mb-6">
                      <h2 className="text-xl font-semibold text-white mb-4">
                        Resources
                      </h2>
                      <div className="space-y-4">
                        <div className="bg-gray-800 rounded-lg p-4">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-400">Treasury</span>
                            <span className="text-white font-medium">
                              {resources.treasury.toLocaleString()}
                            </span>
                          </div>
                        </div>

                        <div className="bg-gray-800 rounded-lg p-4">
                          <h3 className="text-white font-medium mb-3">
                            Resource Reserves
                          </h3>
                          <div className="grid grid-cols-2 gap-4">
                            {Object.entries(resources.resourceReserves).map(
                              ([resource, amount]) =>
                                amount > 0 && (
                                  <div
                                    key={resource}
                                    className="flex justify-between items-center"
                                  >
                                    <span className="text-gray-400 capitalize">
                                      {resource}
                                    </span>
                                    <span className="text-white">
                                      {amount.toLocaleString()}
                                    </span>
                                  </div>
                                )
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Diplomacy Section */}
                  <div className="bg-gray-700/30 rounded-lg p-6">
                    <h2 className="text-xl font-semibold text-white mb-4">
                      Diplomacy
                    </h2>
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-white font-medium mb-3">Allies</h3>
                        {!state.allies || state.allies.length === 0 ? (
                          <p className="text-gray-400">No allies</p>
                        ) : (
                          <ul className="space-y-2">
                            {state.allies.map((allyId) => (
                              <li key={allyId}>
                                <Link
                                  to={`/states/${allyId}`}
                                  className="text-blue-400 hover:text-blue-300"
                                >
                                  View Ally
                                </Link>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>

                      <div>
                        <h3 className="text-white font-medium mb-3">Enemies</h3>
                        {!state.enemies || state.enemies.length === 0 ? (
                          <p className="text-gray-400">No enemies</p>
                        ) : (
                          <ul className="space-y-2">
                            {state.enemies.map((enemyId) => (
                              <li key={enemyId}>
                                <Link
                                  to={`/states/${enemyId}`}
                                  className="text-blue-400 hover:text-blue-300"
                                >
                                  View Enemy
                                </Link>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Elections Tab */}
            {activeTab === 'elections' && (
              <div className="space-y-6">
                {electionsLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto"></div>
                    <p className="text-gray-400 mt-2">Loading elections...</p>
                  </div>
                ) : (
                  <>
                    {/* Current Election */}
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-4">Current Election</h3>
                      {activeElection ? (
                        <ActiveElection
                          election={activeElection}
                          stateId={state.id}
                        />
                      ) : (
                        <div className="bg-gray-700/30 rounded-lg p-8 text-center">
                          <FaVoteYea className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                          <h4 className="text-xl font-semibold text-white mb-2">No Active Election</h4>
                          <p className="text-gray-400 mb-4">
                            There is currently no active election in {state.name}.
                          </p>
                          <p className="text-gray-500 text-sm">
                            Elections are typically held every few months. Check back later or view past elections below.
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Election History */}
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-4">Election History</h3>
                      <ElectionHistory stateId={state.id} />
                    </div>
                  </>
                )}
              </div>
            )}

            {/* Wars Tab */}
            {activeTab === 'wars' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-white">State Wars</h3>
                  <button
                    onClick={fetchWarsData}
                    disabled={warsLoading}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
                  >
                    {warsLoading ? 'Loading...' : 'Refresh'}
                  </button>
                </div>

                {warsLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto"></div>
                    <p className="text-gray-400 mt-2">Loading wars...</p>
                  </div>
                ) : stateWars.length === 0 ? (
                  <div className="bg-gray-700/30 rounded-lg p-8 text-center">
                    <FaFistRaised className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-white mb-2">No Active Wars</h4>
                    <p className="text-gray-400">
                      {state.name} is not currently involved in any wars.
                    </p>
                  </div>
                ) : (
                  <div className="grid gap-4">
                    {stateWars.map((war) => (
                      <div key={war.id} className="bg-gray-700/30 rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="text-lg font-semibold text-white">
                            {war.warTarget} War
                          </h4>
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            war.status === 'active' ? 'bg-red-900/50 text-red-400' : 'bg-gray-600 text-gray-300'
                          }`}>
                            {war.status}
                          </span>
                        </div>

                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <p className="text-gray-400 text-sm">Attacker</p>
                            <p className="text-white">
                              {war.attackerState?.name || war.attackerRegion?.name || 'Unknown'}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-400 text-sm">Defender</p>
                            <p className="text-white">
                              {war.defenderState?.name || war.defenderRegion?.name || 'Unknown'}
                            </p>
                          </div>
                        </div>

                        <Link
                          to={`/wars/${war.id}`}
                          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                        >
                          View Details
                        </Link>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Government Tab - State Leaders Only */}
            {activeTab === 'government' && isStateLeader && (
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white mb-6">Government Management</h3>

                {/* Government Type Section */}
                <div className="bg-gray-700/30 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-white mb-4">Government Type</h4>
                  <p className="text-gray-400 mb-4">
                    Current government type: <span className="text-white font-medium capitalize">
                      {state.governmentType || 'republic'}
                    </span>
                  </p>

                  <div className="flex space-x-4">
                    <button
                      onClick={() => handleGovernmentTypeChange(GovernmentType.REPUBLIC)}
                      disabled={isUpdatingGovernment || state.governmentType === GovernmentType.REPUBLIC}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                    >
                      {isUpdatingGovernment ? 'Updating...' : 'Set Republic'}
                    </button>
                    <button
                      onClick={() => handleGovernmentTypeChange(GovernmentType.DICTATORSHIP)}
                      disabled={isUpdatingGovernment || state.governmentType === GovernmentType.DICTATORSHIP}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                    >
                      {isUpdatingGovernment ? 'Updating...' : 'Set Dictatorship'}
                    </button>
                  </div>
                </div>

                {/* Border Control Section */}
                <div className="bg-gray-700/30 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-white mb-4">Border Control</h4>
                  <p className="text-gray-400 mb-4">
                    Current border status: <span className="text-white font-medium">
                      {state.hasOpenBorders ? 'Open' : 'Closed'}
                    </span>
                  </p>
                  <p className="text-gray-500 text-sm mb-4">
                    {state.hasOpenBorders
                      ? 'Citizens can travel to your state without permission requests.'
                      : 'Citizens need permission to travel to your state.'
                    }
                  </p>

                  <button
                    onClick={handleBorderToggle}
                    disabled={isUpdatingBorders}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                      state.hasOpenBorders
                        ? 'bg-red-600 hover:bg-red-700 text-white'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    } disabled:bg-gray-600 disabled:cursor-not-allowed`}
                  >
                    {state.hasOpenBorders ? <FaLock className="w-4 h-4" /> : <FaUnlock className="w-4 h-4" />}
                    <span>
                      {isUpdatingBorders
                        ? 'Updating...'
                        : state.hasOpenBorders
                          ? 'Close Borders'
                          : 'Open Borders'
                      }
                    </span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <Footer />

      {/* Government Type Change Confirmation Modal */}
      {showGovernmentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold text-white mb-4">
              Change to Republic Government?
            </h3>
            <div className="mb-6">
              <p className="text-gray-300 mb-3">
                Are you sure you want to change the government type to Republic?
              </p>
              <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                <p className="text-blue-300 text-sm">
                  <strong>⚠️ Important:</strong> Changing to Republic will automatically start elections in your state.
                  Citizens will be able to vote for a new leader, and you may lose your leadership position.
                </p>
              </div>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={handleModalCancel}
                disabled={isUpdatingGovernment}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleModalConfirm}
                disabled={isUpdatingGovernment}
                className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-700 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {isUpdatingGovernment ? 'Starting Elections...' : 'Yes, Start Elections'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default StateDetail;
