import { useState } from 'react';
import { Calendar, Users, TrendingUp, Info, RefreshCw } from 'lucide-react';
import ElectionCountdown from './ElectionCountdown';
import VotingInterface from './VotingInterface';
import CandidateCard from './CandidateCard';
import useElectionStore from '../../store/useElectionStore';
import { ELECTION_CONFIG } from '../../config/election.config';

const ActiveElection = ({ election: initialElection, stateId }) => {
  const [activeTab, setActiveTab] = useState('vote');
  const [election, setElection] = useState(initialElection);
  const { refreshActiveElection, loading } = useElectionStore();

  const handleElectionEnd = async () => {
    // Refresh election data when countdown ends
    try {
      const updatedElection = await refreshActiveElection(stateId);
      if (updatedElection) {
        setElection(updatedElection);
      }
    } catch (error) {
      console.error('Failed to refresh election after end:', error);
    }
  };

  const handleVoteSubmitted = async () => {
    // Refresh election data after vote submission
    try {
      const updatedElection = await refreshActiveElection(stateId);
      if (updatedElection) {
        setElection(updatedElection);
      }
    } catch (error) {
      console.error('Failed to refresh election after vote:', error);
    }
  };

  const handleRefresh = async () => {
    try {
      const updatedElection = await refreshActiveElection(stateId);
      if (updatedElection) {
        setElection(updatedElection);
      }
    } catch (error) {
      console.error('Failed to refresh election:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const tabs = [
    { id: 'vote', label: 'Vote', icon: Users },
    { id: 'results', label: 'Live Results', icon: TrendingUp },
    { id: 'info', label: 'Information', icon: Info }
  ];

  return (
    <div className="space-y-6">
      {/* Election Header */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white">
            {election.state.name} State Election
          </h2>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
            title="Refresh election data"
          >
            <RefreshCw className={`w-5 h-5 text-gray-400 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>

        {/* Election Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center space-x-2 text-blue-400 mb-1">
              <Users className="w-4 h-4" />
              <span className="text-sm">Total Votes</span>
            </div>
            <p className="text-2xl font-bold text-white">{election.totalVotes || 0}</p>
          </div>
          {/* <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center space-x-2 text-green-400 mb-1">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm">Voter Turnout</span>
            </div>
            <p className="text-2xl font-bold text-white">{(election.voterTurnout || 0).toFixed(1)}%</p>
          </div> */}
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center space-x-2 text-purple-400 mb-1">
              <Users className="w-4 h-4" />
              <span className="text-sm">Candidates</span>
            </div>
            <p className="text-2xl font-bold text-white">{(election.candidates || []).length}</p>
          </div>
        </div>

        {/* Election Dates */}
        <div className="flex items-center space-x-6 text-sm text-gray-400">
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4" />
            <span>Started: {formatDate(election.startedAt || election.startTime)}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4" />
            <span>Ends: {election.endedAt ? formatDate(election.endedAt) : 'TBD'}</span>
          </div>
        </div>
      </div>

      {/* Countdown Timer */}
      {election.endedAt && (
        <ElectionCountdown
          endTime={election.endedAt}
          onElectionEnd={handleElectionEnd}
        />
      )}

      {/* Tab Navigation */}
      <div className="bg-gray-800 rounded-lg">
        <div className="flex border-b border-gray-700">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 px-6 py-4 font-medium transition-colors
                  ${activeTab === tab.id
                    ? 'text-blue-400 border-b-2 border-blue-400 bg-blue-900/20'
                    : 'text-gray-400 hover:text-gray-300'
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'vote' && (
            <VotingInterface
              election={election}
              onVoteSubmitted={handleVoteSubmitted}
            />
          )}

          {activeTab === 'results' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white mb-4">Live Results</h3>
              {(election.candidates || []).length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(election.candidates || [])
                    .sort((a, b) => (b.voteCount || 0) - (a.voteCount || 0))
                    .map((candidate) => (
                      <CandidateCard
                        key={candidate.id}
                        candidate={candidate}
                        totalVotes={election.totalVotes || 0}
                        isSelected={false}
                        hasUserVoted={election.hasUserVoted}
                        showVoteButton={false}
                        showResults={true}
                      />
                    ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400">No candidates registered yet</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'info' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white mb-4">Election Information</h3>
              <div className="bg-gray-700 rounded-lg p-4 space-y-3">
                <div>
                  <h4 className="font-medium text-white mb-1">Duration</h4>
                  <p className="text-gray-300 text-sm">
                    This election runs for {election.duration || ELECTION_CONFIG.DEFAULT_DURATION_HOURS} hours ({Math.floor((election.duration || ELECTION_CONFIG.DEFAULT_DURATION_HOURS) / 24)} day) from start to finish.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-1">Eligibility</h4>
                  <p className="text-gray-300 text-sm">
                    Only residents of {election.state.name} are eligible to vote in this election.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-1">Voting Rules</h4>
                  <p className="text-gray-300 text-sm">
                    Each eligible voter can cast one vote for their preferred candidate.
                    Votes cannot be changed once submitted.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-1">Winner Determination</h4>
                  <p className="text-gray-300 text-sm">
                    The candidate with the most votes at the end of the election period will be declared the winner.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActiveElection;
