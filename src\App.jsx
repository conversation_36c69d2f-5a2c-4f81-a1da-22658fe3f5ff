import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Suspense, lazy } from "react";
import { useAuthGuard } from './hooks/useAuthGuard';
const LandingPage = lazy(() => import("./pages/LandingPage"));
const Home = lazy(() => import("./pages/Home"));
const Login = lazy(() => import("./pages/Login"));
const Register = lazy(() => import("./pages/Register"));
const ForgotPassword = lazy(() => import("./pages/ForgotPassword"));
const ResetPassword = lazy(() => import("./pages/ResetPassword"));
const Profile = lazy(() => import("./pages/Profile"));
const UserProfile = lazy(() => import("./pages/UserProfile"));
const MapPage = lazy(() => import("./pages/MapPage"));
const VerifyAccount = lazy(() => import("./pages/VerifyAccount"));
const JobsPage = lazy(() => import("./pages/JobsPage"));
const ShopPage = lazy(() => import("./pages/ShopPage"));
const PaymentSuccessPage = lazy(() => import("./pages/PaymentSuccessPage"));
const PaymentCancelPage = lazy(() => import("./pages/PaymentCancelPage"));
const StatesList = lazy(() => import("./components/states/StatesList"));
const StateDetail = lazy(() => import("./components/states/StateDetail"));
const MyStateRedirect = lazy(() => import("./components/states/MyStateRedirect"));
const WarsPage = lazy(() => import("./pages/WarsPage"));
const WarDetailPage = lazy(() => import("./pages/WarDetailPage"));
const DeclareWarPage = lazy(() => import("./pages/DeclareWarPage"));
const WarAnalyticsPage = lazy(() => import("./pages/WarAnalyticsPage"));
const CreateParty = lazy(() => import("./pages/CreateParty"));
const PartyDetailPage = lazy(() => import("./pages/PartyDetailPage"));
const TravelPermissions = lazy(() => import("./pages/TravelPermissions"));
const RegionDetailPage = lazy(() => import("./pages/RegionDetailPage"));

// Create an AuthGuard component to protect routes
const AuthGuard = ({ children }) => {
  // Call the hook to perform the authentication check
  useAuthGuard();

  // If the hook doesn't redirect, render the children
  return children;
};

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Routes>
        {/* Public routes */}
        <Route path="/" element={<LandingPage />} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/reset-password" element={<ResetPassword />} />
        <Route path="/verify-account" element={<VerifyAccount />} />

        {/* Protected routes */}
        <Route
          path="/profile"
          element={
            <AuthGuard>
              <Profile />
            </AuthGuard>
          }
        />
        <Route
          path="/home"
          element={
            <AuthGuard>
              <Home />
            </AuthGuard>
          }
        />
        <Route
          path="/map"
          element={
            <AuthGuard>
              <MapPage />
            </AuthGuard>
          }
        />
        <Route
          path="/jobs"
          element={
            <AuthGuard>
              <JobsPage />
            </AuthGuard>
          }
        />
        <Route
          path="/party/create"
          element={
            <AuthGuard>
              <CreateParty />
            </AuthGuard>
          }
        />
        <Route
          path="/party/:id"
          element={
            <AuthGuard>
              <PartyDetailPage />
            </AuthGuard>
          }
        />
        <Route
          path="/shop"
          element={
            <AuthGuard>
              <ShopPage />
            </AuthGuard>
          }
        />
        <Route
          path="/payment/success"
          element={
            <AuthGuard>
              <PaymentSuccessPage />
            </AuthGuard>
          }
        />
        <Route
          path="/payment/cancel"
          element={
            <AuthGuard>
              <PaymentCancelPage />
            </AuthGuard>
          }
        />

        {/* State routes - protected */}
        <Route
          path="/states"
          element={
            <AuthGuard>
              <StatesList />
            </AuthGuard>
          }
        />
        <Route
          path="/states/my-state"
          element={
            <AuthGuard>
              <MyStateRedirect />
            </AuthGuard>
          }
        />
        <Route
          path="/states/:id"
          element={
            <AuthGuard>
              <StateDetail />
            </AuthGuard>
          }
        />

        {/* War routes - protected */}
        <Route
          path="/wars"
          element={
            <AuthGuard>
              <WarsPage />
            </AuthGuard>
          }
        />
        <Route
          path="/wars/new"
          element={
            <AuthGuard>
              <DeclareWarPage />
            </AuthGuard>
          }
        />
        <Route
          path="/wars/:id"
          element={
            <AuthGuard>
              <WarDetailPage />
            </AuthGuard>
          }
        />
        <Route
          path="/wars/analytics"
          element={
            <AuthGuard>
              <WarAnalyticsPage />
            </AuthGuard>
          }
        />

        {/* User routes - protected */}
        <Route
          path="/users/:id"
          element={
            <AuthGuard>
              <UserProfile />
            </AuthGuard>
          }
        />

        {/* Region routes - protected */}
        {/* <Route path="/regions" element={<AuthGuard><RegionsList /></AuthGuard>} /> */}
        <Route
          path="/regions/:id"
          element={
            <AuthGuard>
              <RegionDetailPage />
            </AuthGuard>
          }
        />

        {/* Travel routes - protected */}
        <Route
          path="/travel/permissions"
          element={
            <AuthGuard>
              <TravelPermissions />
            </AuthGuard>
          }
        />



        {/* Redirect to home if logged in, landing if not */}
        <Route path="*" element={<Navigate to="/home" replace />} />
      </Routes>
    </Suspense>
  );
}

export default App;
