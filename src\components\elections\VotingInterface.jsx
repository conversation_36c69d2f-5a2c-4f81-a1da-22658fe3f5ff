import React, { useState } from 'react';
import { Vote, CheckCircle, AlertCircle, Users } from 'lucide-react';
import CandidateCard from './CandidateCard';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { showErrorToast } from '../../utils/showErrorToast';
import useElectionStore from '../../store/useElectionStore';
import useUserDataStore from '../../store/useUserDataStore';

const VotingInterface = ({ election, onVoteSubmitted }) => {
  const [selectedCandidateId, setSelectedCandidateId] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { submitVote } = useElectionStore();
  const { userData } = useUserDataStore();

  // Debug logging for voting eligibility
  console.log('🗳️ Voting Interface Debug:', {
    electionState: election.state?.name,
    electionStateId: election.state?.id,
    userRegion: userData?.region?.name,
    userRegionId: userData?.region?.id,
    userState: userData?.region?.state?.name,
    userStateId: userData?.region?.state?.id,
    isUserEligible: election.isUserEligible,
    hasUserVoted: election.hasUserVoted,
    isUserInSameState: userData?.region?.state?.id === election.state?.id
  });

  const selectedCandidate = (election.candidates || []).find(c =>
    (c.id === selectedCandidateId) ||
    (c.userId === selectedCandidateId) ||
    (c.user?.id === selectedCandidateId)
  );

  const handleCandidateSelect = (candidateId) => {
    setSelectedCandidateId(candidateId);
  };

  const handleVoteClick = () => {
    if (!selectedCandidateId) {
      showErrorToast('Please select a candidate before voting');
      return;
    }
    setShowConfirmation(true);
  };

  const handleConfirmVote = async () => {
    if (!selectedCandidateId) return;

    setIsSubmitting(true);
    try {
      await submitVote(election.id, selectedCandidateId);
      showSuccessToast('Your vote has been successfully submitted!');
      setShowConfirmation(false);
      if (onVoteSubmitted) {
        onVoteSubmitted();
      }
    } catch (error) {
      console.log(error,'error');
      
      console.error('Failed to submit vote:', error);
      showErrorToast(error || 'Failed to submit vote. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelVote = () => {
    setShowConfirmation(false);
  };

  // If user has already voted, show their vote
  if (election.hasUserVoted && election.userVote) {
    return (
      <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <CheckCircle className="w-6 h-6 text-green-400" />
          <h3 className="text-lg font-semibold text-white">Vote Submitted</h3>
        </div>
        <p className="text-gray-300 mb-4">
          You have successfully voted in this election. Thank you for participating!
        </p>
        <div className="bg-gray-800 rounded-lg p-4">
          <p className="text-sm text-gray-400 mb-2">Your vote:</p>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
              <Users className="w-4 h-4 text-gray-400" />
            </div>
            <div>
              <p className="text-white font-medium">{election.userVote.candidate.user.username}</p>
              {election.userVote.candidate.party && (
                <p className="text-sm text-gray-400">{election.userVote.candidate.party.name}</p>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If user is not eligible to vote
  if (!election.isUserEligible) {
    return (
      <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <AlertCircle className="w-6 h-6 text-yellow-400" />
          <h3 className="text-lg font-semibold text-white">Not Eligible to Vote</h3>
        </div>
        <p className="text-gray-300 mb-4">
          You are not eligible to vote in this election. Only residents of {election.state?.name || 'this state'} can participate.
        </p>

        {/* Debug information */}
        <div className="bg-gray-800 rounded-lg p-4 mt-4">
          <h4 className="text-sm font-semibold text-white mb-2">Debug Information:</h4>
          <div className="text-xs text-gray-400 space-y-1">
            <p><strong>Election State:</strong> {election.state?.name || 'Unknown'} (ID: {election.state?.id || 'Unknown'})</p>
            <p><strong>Your Region:</strong> {userData?.region?.name || 'Unknown'} (ID: {userData?.region?.id || 'Unknown'})</p>
            <p><strong>Your State:</strong> {userData?.region?.state?.name || 'None'} (ID: {userData?.region?.state?.id || 'None'})</p>
            <p><strong>Backend Says Eligible:</strong> {election.isUserEligible ? 'Yes' : 'No'}</p>
            <p><strong>States Match:</strong> {userData?.region?.state?.id === election.state?.id ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Voting Instructions */}
      <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
        <div className="flex items-center space-x-2 text-blue-400 mb-2">
          <Vote className="w-5 h-5" />
          <span className="font-semibold">Cast Your Vote</span>
        </div>
        <p className="text-gray-300 text-sm">
          Select a candidate below and click "Submit Vote" to participate in this election.
          Your vote is final and cannot be changed.
        </p>
      </div>

      {/* Candidates Grid */}
      {(election.candidates || []).length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {(election.candidates || []).map((candidate) => (
            <CandidateCard
              key={candidate.id || candidate.userId}
              candidate={candidate}
              totalVotes={election.totalVotes || 0}
              isSelected={selectedCandidateId === (candidate.id || candidate.userId || candidate.user?.id)}
              onSelect={handleCandidateSelect}
              hasUserVoted={election.hasUserVoted}
              showVoteButton={false}
              showResults={false}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <Users className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">No candidates available for this election yet.</p>
        </div>
      )}

      {/* Vote Submission */}
      <div className="flex justify-center">
        <button
          onClick={handleVoteClick}
          disabled={!selectedCandidateId || isSubmitting}
          className={`
            px-8 py-3 rounded-lg font-semibold transition-all duration-200
            ${selectedCandidateId
              ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
              : 'bg-gray-700 text-gray-400 cursor-not-allowed'
            }
          `}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Vote'}
        </button>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-white mb-4">Confirm Your Vote</h3>
            <p className="text-gray-300 mb-4">
              Are you sure you want to vote for <strong>{selectedCandidate?.user?.username || selectedCandidate?.username}</strong>?
              This action cannot be undone.
            </p>

            {selectedCandidate && (
              <div className="bg-gray-700 rounded-lg p-3 mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-white font-medium">{selectedCandidate.user?.username || selectedCandidate.username}</p>
                    {selectedCandidate.party && (
                      <p className="text-sm text-gray-400">{selectedCandidate.party.name}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            <div className="flex space-x-3">
              <button
                onClick={handleCancelVote}
                disabled={isSubmitting}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmVote}
                disabled={isSubmitting}
                className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                {isSubmitting ? 'Submitting...' : 'Confirm Vote'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VotingInterface;
