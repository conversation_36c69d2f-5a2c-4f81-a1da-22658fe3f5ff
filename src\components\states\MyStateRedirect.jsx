import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { stateService } from '../../services/api/state.service';
import { showErrorToast } from '../../utils/showErrorToast';
import Navbar from '../Navbar';

const MyStateRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUserState = async () => {
      try {
        const userState = await stateService.getUserState();
        if (userState) {
          navigate(`/states/${userState.id}`, { replace: true });
        } else {
          showErrorToast('You are not a member of any state');
          navigate('/states', { replace: true });
        }
      } catch (error) {
        console.error('Error fetching user state:', error);
        showErrorToast('Failed to load your state');
        navigate('/states', { replace: true });
      }
    };

    fetchUserState();
  }, [navigate]);

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your state...</p>
        </div>
      </div>
    </>
  );
};

export default MyStateRedirect;
