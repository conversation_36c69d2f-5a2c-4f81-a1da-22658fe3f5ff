import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { userService } from "../services/api/user.service";
import { regionService } from "../services/api/region.service";
import { stateService } from "../services/api/state.service";
import { warService } from "../services/api/war.service";
import { Users, MapPin, Flag, Sword, TrendingUp, Clock, Crown, Factory, Globe } from "lucide-react";
import Footer from "../components/common/Footer";

interface GameStats {
  totalPlayers: number;
  totalRegions: number;
  totalStates: number;
  activeWars: number;
  totalWars: number;
  ongoingElections: number;
  totalPopulation: number;
}

interface WarTimelineEvent {
  eventType: string;
  description?: string;
  warName: string;
  timestamp: string | Date;
}

export default function LandingPage() {  
  const [stats, setStats] = useState<GameStats>({
    totalPlayers: 0,
    totalRegions: 0,
    totalStates: 0,
    activeWars: 0,
    totalWars: 0,
    ongoingElections: 0,
    totalPopulation: 0,
  });
  const [recentWars, setRecentWars] = useState<WarTimelineEvent[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGameStats = async () => {
      try {
        setLoading(true);
        
        // Fetch all statistics in parallel
        const [
          playersCount,
          regionsData,
          statesData,
          activeWarsData,
          globalWarStats,
          warTimeline,
        ] = await Promise.all([
          userService.getAllUsersCount().catch(() => 0),
          regionService.getAllRegionsCount().catch(() => 0),
          stateService.getAllStatesCount().catch(() => 0),
          warService.getActiveWars().catch(() => []),
          warService.getGlobalWarStats().catch(() => ({ totalWars: 0 })),
          warService.getWarTimeline(5).catch(() => []),
        ]);


        setStats({
          totalPlayers: playersCount,
          totalRegions: regionsData,
          totalStates: statesData,
          activeWars: activeWarsData.length,
          totalWars: globalWarStats.totalWars || 0,
          ongoingElections: 0, // Will be updated if election service is available
          totalPopulation: playersCount,
        });

        setRecentWars(warTimeline);
        console.log(warTimeline,'warTimeline');
      } catch (error) {
        console.error("Error fetching game statistics:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchGameStats();
  }, []);

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>
        <div className="relative max-w-7xl mx-auto px-4 py-24">
          <div className="text-center">
            <img 
              src="/wn-logo.png" 
              alt="Warfront Nations" 
              className="mx-auto mb-8 h-24 w-auto"
            />
            <h1 className="text-6xl font-bold text-neonBlue mb-6">
              Warfront Nations
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Enter a world of strategic warfare, political intrigue, and economic dominance.
              Build your empire, forge alliances, and conquer territories in this immersive multiplayer strategy game.
            </p>
            <div className="flex flex-wrap justify-center gap-4 mb-8 text-sm text-gray-400">
              <span className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Real-time gameplay
              </span>
              <span className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Multiplayer strategy
              </span>
              <span className="flex items-center gap-2">
                <Crown className="h-4 w-4" />
                Political system
              </span>
              <span className="flex items-center gap-2">
                <Sword className="h-4 w-4" />
                Epic battles
              </span>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register">
                <button className="px-8 py-4 bg-neonGreen text-darkBg rounded-lg text-lg font-semibold hover:bg-green-400 transition-colors">
                  Join the Battle
                </button>
              </Link>
              <Link to="/login">
                <button className="px-8 py-4 border-2 border-neonBlue text-neonBlue rounded-lg text-lg font-semibold hover:bg-neonBlue hover:text-darkBg transition-colors">
                  Enter Game
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Game Statistics Section */}
      <div className="py-16 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Live Game Statistics</h2>
            <p className="text-gray-400">Join thousands of players in an ever-evolving world</p>
          </div>
          
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="bg-gray-700 rounded-lg p-6 animate-pulse">
                  <div className="h-12 bg-gray-600 rounded mb-4"></div>
                  <div className="h-8 bg-gray-600 rounded mb-2"></div>
                  <div className="h-4 bg-gray-600 rounded"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              <div className="bg-gray-700 rounded-lg p-6 text-center hover:bg-gray-600 transition-colors">
                <Users className="h-12 w-12 text-neonBlue mx-auto mb-4" />
                <div className="text-3xl font-bold text-white mb-2">
                  {stats.totalPlayers.toLocaleString()}
                </div>
                <div className="text-gray-400">Active Players</div>
              </div>

              {/* <div className="bg-gray-700 rounded-lg p-6 text-center hover:bg-gray-600 transition-colors">
                <Globe className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <div className="text-3xl font-bold text-white mb-2">
                  {stats.totalPopulation.toLocaleString()}
                </div>
                <div className="text-gray-400">Total Population</div>
              </div> */}

              <div className="bg-gray-700 rounded-lg p-6 text-center hover:bg-gray-600 transition-colors">
                <MapPin className="h-12 w-12 text-neonGreen mx-auto mb-4" />
                <div className="text-3xl font-bold text-white mb-2">
                  {stats.totalRegions.toLocaleString()}
                </div>
                <div className="text-gray-400">Regions</div>
              </div>

              <div className="bg-gray-700 rounded-lg p-6 text-center hover:bg-gray-600 transition-colors">
                <Flag className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
                <div className="text-3xl font-bold text-white mb-2">
                  {stats.totalStates.toLocaleString()}
                </div>
                <div className="text-gray-400">States</div>
              </div>

              <div className="bg-gray-700 rounded-lg p-6 text-center hover:bg-gray-600 transition-colors">
                <Sword className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <div className="text-3xl font-bold text-white mb-2">
                  {stats.activeWars.toLocaleString()}
                </div>
                <div className="text-gray-400">Active Wars</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Game Features Section */}
      <div className="py-16 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Game Features</h2>
            <p className="text-gray-400">Experience the ultimate strategy game</p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gray-800 rounded-lg p-6">
              <Sword className="h-12 w-12 text-red-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-3">Strategic Warfare</h3>
              <p className="text-gray-400">
                Engage in tactical battles across land and sea. Plan your attacks, manage resources, 
                and lead your forces to victory in dynamic war scenarios.
              </p>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-6">
              <Crown className="h-12 w-12 text-yellow-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-3">Political System</h3>
              <p className="text-gray-400">
                Run for office, participate in elections, and govern states. Make crucial decisions 
                that affect your citizens and shape the political landscape.
              </p>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-6">
              <Factory className="h-12 w-12 text-neonGreen mb-4" />
              <h3 className="text-xl font-bold text-white mb-3">Economic Empire</h3>
              <p className="text-gray-400">
                Build and manage factories, control resources, and develop your economic power. 
                Trade with other players and dominate markets.
              </p>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-6">
              <Users className="h-12 w-12 text-neonBlue mb-4" />
              <h3 className="text-xl font-bold text-white mb-3">Social Dynamics</h3>
              <p className="text-gray-400">
                Form parties, create alliances, and engage in diplomacy. Build relationships 
                that can make or break your empire.
              </p>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-6">
              <MapPin className="h-12 w-12 text-purple-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-3">Territory Control</h3>
              <p className="text-gray-400">
                Expand your influence across regions, manage populations, and control strategic 
                locations to build your empire.
              </p>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-6">
              <TrendingUp className="h-12 w-12 text-green-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-3">Character Development</h3>
              <p className="text-gray-400">
                Train your character's skills, improve your combat effectiveness, and unlock 
                new abilities as you progress through the game.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity Section */}
      {recentWars.length > 0 && (
        <div className="py-16 bg-gray-800">
          <div className="max-w-7xl mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">Recent War Activity</h2>
              <p className="text-gray-400">Stay updated with the latest conflicts</p>
            </div>

            <div className="space-y-4 max-w-4xl mx-auto">
              {recentWars.slice(0, 5).map((event, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <Clock className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-white font-medium">
                        {event.warName}
                      </div>
                      <div className="text-gray-400 text-sm">
                        {event.description}
                      </div>
                    </div>
                  </div>
                  <div className="text-gray-400 text-sm">
                    {new Date(event.timestamp).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Call to Action Section */}
      <div className="py-16 bg-gradient-to-r from-neonBlue to-neonGreen">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-darkBg mb-6">
            Ready to Build Your Empire?
          </h2>
          <p className="text-xl text-darkBg mb-8 opacity-90">
            Join thousands of players in the ultimate strategy experience.
            Your journey to power starts now.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register">
              <button className="px-8 py-4 bg-darkBg text-neonGreen rounded-lg text-lg font-semibold hover:bg-gray-800 transition-colors">
                Create Account
              </button>
            </Link>
            <Link to="/login">
              <button className="px-8 py-4 border-2 border-darkBg text-darkBg rounded-lg text-lg font-semibold hover:bg-darkBg hover:text-neonGreen transition-colors">
                Login Now
              </button>
            </Link>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
